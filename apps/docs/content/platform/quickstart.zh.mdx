---
title: "快速开始"
description: "使用 Libra AI 平台将创意快速转化为生产就绪的 Web 应用程序"
mode: "center"
icon: Rocket
---
欢迎来到 Libra！本指南将帮助您创建和定制您的第一个 AI 网站。让我们开始吧！

## 从概念到实时 Web 应用

本指南将指导您使用 Libra 的 AI 驱动平台，将您的创意转变为生产就绪的 Web 应用程序。按照以下简单步骤，将您的创意变为现实，而无需传统的开发烦恼。

## 1. 定义您的创意

首先清晰地表达您的 Web 应用创意。精心编写的提示将为您的创意量身定制代码库奠定基础。

**示例提示：**

```
"为一家科技初创公司开发一个现代的着陆页，具有交互式 UI 元素和大胆鲜艳的颜色。"
```

> **提示技巧**：描述得越具体，生成的应用程序就越符合您的期望。包括设计风格、功能需求和目标用户等详细信息。

![Libra 平台操作演示](https://media.libra.dev/Area.gif)

## 2. 完善您的创意

使用"增强提示"功能，让 Libra 的高级 AI 完善您的初始概念。此步骤有助于确保生成的 Web 应用与您的期望紧密相符。

- **查看 AI 建议**：检查增强内容并根据需要进行微调
- **确认细节**：调整并最终确定您的提示以获得最佳结果

## 3. 生成您的应用程序

一旦您的提示完善后，点击"提交"按钮以启动生成过程。Libra 将把您的增强提示转换为基于生产就绪的 React 栈的完全定制代码库。

> **生成过程**：通常需要几分钟时间，系统会实时显示生成进度。

## 4. 编辑和自定义

进入 Libra 工作区以微调您的项目。通过以下方式进一步增强您的应用程序：

- **迭代编辑**：利用 AI 驱动的建议进行持续改进
- **组件集成**：无缝添加和自定义 UI 组件，如弹出窗口、按钮和输入字段
- **可选的 AI 增强功能**：集成 OpenAI 功能以提升您的应用程序的功能


## 5. 导出您的项目

当您准备就绪时，选择导出方法以保存您的作品：

- **文件下载**：将您的项目导出为 ZIP 文件
- **GitHub 导出**：自动在 GitHub 上创建一个新的存储库，其中包含您的项目代码

## 6. 部署您的 Web 应用

一键部署您的应用程序。享受无缝的托管集成和持续交付，确保您的 Web 应用从一开始就安全、可扩展且实时运行。

> **部署选项**：目前支持在 Cloudflare Workers 上部署。对 Vercel、Netlify 等其他平台的支持计划在未来版本中推出。

⚠️：现阶段只支持部署在 Cloudflare 上

## 下一步

按照这些步骤，您就可以使用 Libra 将您的创意转变为生产就绪的 Web 应用程序。让我们的强大 AI 工具处理技术细节，同时您专注于将您的创意变为现实。

**推荐后续操作：**
- 查看 [使用指南](/platform/usinglibra) 了解更多高级功能
- 访问 [常见问题](/platform/faq) 获取常见问题解答
- 加入我们的 [GitHub 社区](https://github.com/saasfly/libra/discussions) 与其他开发者交流

祝您构建愉快！
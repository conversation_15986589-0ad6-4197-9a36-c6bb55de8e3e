---
title: "FAQ"
mode: "center"
icon: CircleHelp
---

# Frequently Asked Questions

Welcome to the Libra AI platform FAQ page! This comprehensive guide answers the most common questions about using the Libra platform, from getting started to advanced features. If you can't find the answer you need here, don't hesitate to contact us through our GitHub community, where our team and community members are ready to help.

This FAQ covers everything from basic functionality to advanced features, providing solutions to technical issues and practical tips on how to use the platform efficiently. Through this guide, you can quickly find the information you need to make full use of Libra's powerful features for a smoother, more efficient development experience.

## Getting Started

### What can I build with Libra?

Libra enables you to create a wide range of applications, including user-facing platforms, business tools, and MVPs for market validation. Discover endless possibilities and draw inspiration from our thriving community.

You can create various types of applications, including:

- **Content Websites**: Personal blogs, portfolios, corporate websites, and technical documentation
- **E-commerce Platforms**: Online stores with integrated payment processing and inventory management
- **Productivity Tools**: Task management systems, note-taking applications, and team collaboration platforms
- **Social Applications**: Community forums, communication platforms, and social networks
- **Enterprise Applications**: Customer relationship management systems, data dashboards, and business analytics platforms
- **Education Platforms**: Online learning systems and course management websites

**Community Showcase:** When you visit the platform, you'll discover various tools and applications created by our community. From functional prototypes to experimental projects, this collection showcases Libra's unlimited possibilities.

### How do I start my project?

Starting a project with Libra is simple and flexible. Choose the method that best suits your preferences and resources:

#### Prompt-based Development
Libra's prompt-based system makes application creation intuitive. Simply describe what you want to build in the prompt box. The more specific your description, the better the results.

**Example Prompt:** "Create a modern landing page for a SaaS startup. The design should feature a deep purple gradient background with glowing futuristic effects."

Start with clear and detailed prompts, then refine or adjust your project as needed.

## Project Configuration and Management

### How do I enable a database to store data?

Database functionality is not currently supported. Please stay tuned for updates.

### How do I enable AI in my application?

If your application requires AI functionality (such as AI assistance, smart recommendations, etc.), built-in AI features will be automatically enabled. No additional setup or configuration is required.

### How do I change the domain name?

Click "Site" in the sidebar to access the domain settings page.

**For free users:** Custom domains cannot be set; only the default libra.sh subdomain is available.

**For Pro or Max users:** After completing deployment, you can add custom domains in the domain settings.

### How do I remove the "Built with Libra" badge from deployed pages?

For free users, the "Built with Libra" badge cannot be removed. If you upgrade to a Pro or Max plan, the badge will be automatically removed.

## Troubleshooting

### How do I fix "blank preview page" issues?

If the preview page appears blank, try these steps:

1. **Refresh the page**: First try refreshing the browser page
2. **Restore previous version**: Restore to a previous version by clicking the "History" button in the top-left corner of the preview window. This allows you to roll back to the last working version
3. **Request AI fix**: If the page is still blank, type in chat: "I see a blank page, please check and fix."
4. **Fork project**: Create a copy of the project, then delete the problematic original project

### How do I fix errors in the browser console?

For some errors, you can use the autofix button for automatic repair.

If you encounter errors in the browser console, copy the error message and paste it into the chat. AI can automatically diagnose and fix the issue.

**Example:**
```
Please fix error: react-dom.production.min.js:121 ReferenceError: useState is not defined at Dashboard ((index):194:88)
```

AI will analyze the error and provide appropriate fixes.

### Does fixing errors consume AI credits?

No, fixing errors does not consume any AI credits. This feature allows you to focus on perfecting your project without worrying about AI credit usage or additional costs. Note: To prevent abuse, we have implemented some limitations on the error-fixing functionality.

## Code Export and Deployment

### Can I export my website's code files?

Yes, you can export your website's code files without deploying the project. To do this:

1. Navigate to the website project you want to download
2. Click the "Code" tab at the top of the preview panel
3. Click the "Download Files" button in the bottom-left corner to download the code files

**Note:** Exported files do not include data from databases or any AI functionality related to the project. What's exported are static code files that you can deploy in local environments or other platforms.

## Community and Support

### How do I get help?

We provide multiple support channels:

| Issue Type | Recommended Channel | Response Time |
|------------|-------------------|---------------|
| 🐛 **Bug Reports** | [GitHub Issues](https://github.com/saasfly/libra/issues) | 1-2 business days |
| 💡 **Feature Requests** | [GitHub Discussions](https://github.com/saasfly/libra/discussions) | 3-5 business days |
| ❓ **Usage Questions** | [GitHub Discussions](https://github.com/saasfly/libra/discussions) | 1-2 business days |
| 🏢 **Business Inquiries** | [<EMAIL>](mailto:<EMAIL>) | 1 business day |

### Is there a community forum?

Yes! Join our active GitHub community where you can:

- Get help from the community and team
- Share your projects and get feedback
- Participate in technical discussions and exchanges
- Access feature previews and updates
- Connect with other developers

Visit our [GitHub Discussions](https://github.com/saasfly/libra/discussions) to get started.

### How do I stay updated on new features?

Follow these channels for the latest updates:

- 🐦 **Twitter**: [@nextify2024](https://x.com/nextify2024) for development updates
- 📖 **Documentation**: [docs.libra.dev](https://docs.libra.dev/) for feature guides
- 🌐 **Website**: [libra.dev](https://libra.dev) for major version releases

## Still have questions?

If you can't find the answer to your question in this FAQ, we're here to help:

- 📧 **Email us**: [<EMAIL>](mailto:<EMAIL>) for business inquiries
- 🐛 **Report issues**: [GitHub Issues](https://github.com/saasfly/libra/issues) for bugs and technical problems
- 💡 **Request features**: [GitHub Discussions](https://github.com/saasfly/libra/discussions) for feature suggestions

Our community and team are always ready to help you succeed on the Libra AI platform!
